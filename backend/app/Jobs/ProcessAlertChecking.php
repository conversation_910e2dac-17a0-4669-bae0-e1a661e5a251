<?php

namespace App\Jobs;

use App\Models\ProductData;
use App\Services\AlertService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessAlertChecking implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 产品数据ID
     */
    public $productDataId;

    /**
     * 采集批次ID
     */
    public $collectionBatchId;

    /**
     * 任务执行超时时间（秒）
     */
    public int $timeout = 120;

    /**
     * 最大重试次数
     */
    public int $tries = 3;

    /**
     * 创建新的任务实例
     */
    public function __construct($productDataId, $collectionBatchId = null)
    {
        $this->productDataId = $productDataId;
        $this->collectionBatchId = $collectionBatchId;
    }

    /**
     * 执行任务
     */
    public function handle(AlertService $alertService): void
    {
        Log::info('开始处理预警检查任务', [
            'product_data_id' => $this->productDataId,
            'collection_batch_id' => $this->collectionBatchId,
        ]);

        try {
            // 获取产品数据
            $productData = ProductData::with(['monitoringTask.dataSource'])->find($this->productDataId);

            if (!$productData) {
                Log::warning('产品数据不存在，跳过预警检查', [
                    'product_data_id' => $this->productDataId,
                ]);
                return;
            }

            // 执行预警检查
            $result = $alertService->processAlerts($productData, $this->collectionBatchId);

            Log::info('预警检查任务完成', [
                'product_data_id' => $this->productDataId,
                'success' => $result['success'] ?? false,
                'alerts_count' => $result['alerts_count'] ?? 0,
            ]);

        } catch (\Exception $e) {
            Log::error('预警检查任务执行失败', [
                'product_data_id' => $this->productDataId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('预警检查任务最终失败', [
            'product_data_id' => $this->productDataId ?? 'unknown',
            'collection_batch_id' => $this->collectionBatchId ?? 'unknown',
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * 获取任务的唯一标识符
     */
    public function uniqueId(): string
    {
        return "alert_check_" . ($this->productDataId ?? 'unknown');
    }
} 