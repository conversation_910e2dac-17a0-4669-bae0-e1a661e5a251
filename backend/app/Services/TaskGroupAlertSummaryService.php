<?php

namespace App\Services;

use App\Models\Alert;
use App\Models\TaskGroup;
use App\Models\MonitoringTask;
use App\Models\ProductData;
use App\Models\TaskGroupAlertSummary;
use App\Jobs\SendTaskGroupAlertNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class TaskGroupAlertSummaryService
{
    /**
     * 为任务分组创建预警汇总
     *
     * @param int $taskGroupId
     * @param string $collectionBatchId
     * @return TaskGroupAlertSummary
     */
    public function createSummaryForTaskGroup(int $taskGroupId, string $collectionBatchId): TaskGroupAlertSummary
    {
        Log::info('为任务分组创建预警汇总', [
            'task_group_id' => $taskGroupId,
            'collection_batch_id' => $collectionBatchId,
        ]);

        // 检查是否已存在相同批次的汇总
        $existingSummary = TaskGroupAlertSummary::where('task_group_id', $taskGroupId)
            ->where('collection_batch_id', $collectionBatchId)
            ->first();

        if ($existingSummary) {
            Log::debug('找到现有的预警汇总记录', [
                'summary_id' => $existingSummary->id,
                'status' => $existingSummary->status,
            ]);
            return $existingSummary;
        }

        // 创建新的汇总记录
        $summary = TaskGroupAlertSummary::create([
            'task_group_id' => $taskGroupId,
            'collection_batch_id' => $collectionBatchId,
            'summary_data' => [],
            'status' => TaskGroupAlertSummary::STATUS_COLLECTING,
        ]);

        Log::info('创建了新的预警汇总记录', [
            'summary_id' => $summary->id,
        ]);

        return $summary;
    }

    /**
     * 为监控任务创建预警汇总
     *
     * @param int $monitoringTaskId
     * @param string $collectionBatchId
     * @return TaskGroupAlertSummary
     */
    public function createSummaryForMonitoringTask(int $monitoringTaskId, string $collectionBatchId): TaskGroupAlertSummary
    {
        $monitoringTask = MonitoringTask::find($monitoringTaskId);
        
        if (!$monitoringTask) {
            throw new \Exception("监控任务不存在: {$monitoringTaskId}");
        }

        Log::info('为监控任务创建预警汇总', [
            'monitoring_task_id' => $monitoringTaskId,
            'task_group_id' => $monitoringTask->task_group_id,
            'collection_batch_id' => $collectionBatchId,
        ]);

        // 检查是否已存在相同批次的汇总
        $existingSummary = TaskGroupAlertSummary::where('monitoring_task_id', $monitoringTaskId)
            ->where('collection_batch_id', $collectionBatchId)
            ->first();

        if ($existingSummary) {
            Log::debug('找到现有的预警汇总记录', [
                'summary_id' => $existingSummary->id,
                'status' => $existingSummary->status,
            ]);
            return $existingSummary;
        }

        // 创建新的汇总记录
        $summary = TaskGroupAlertSummary::create([
            'task_group_id' => $monitoringTask->task_group_id,
            'monitoring_task_id' => $monitoringTaskId,
            'collection_batch_id' => $collectionBatchId,
            'summary_data' => [],
            'status' => TaskGroupAlertSummary::STATUS_COLLECTING,
        ]);

        Log::info('创建了新的预警汇总记录', [
            'summary_id' => $summary->id,
        ]);

        return $summary;
    }

    /**
     * 添加预警到汇总中
     *
     * @param Alert $alert
     * @param string $collectionBatchId
     * @return void
     */
    public function addAlertToSummary(Alert $alert, string $collectionBatchId): void
    {
        Log::debug('添加预警到汇总', [
            'alert_id' => $alert->id,
            'monitoring_task_id' => $alert->monitoring_task_id,
            'collection_batch_id' => $collectionBatchId,
        ]);

        // 获取监控任务
        $monitoringTask = $alert->monitoringTask;
        if (!$monitoringTask) {
            Log::warning('预警没有关联的监控任务，跳过汇总', [
                'alert_id' => $alert->id,
            ]);
            return;
        }

        // 获取或创建汇总记录
        $summary = $this->createSummaryForMonitoringTask($monitoringTask->id, $collectionBatchId);

        // 将预警添加到汇总中
        $summary->addAlert($alert);

        // 更新预警记录的汇总关联
        $alert->update([
            'task_group_alert_summary_id' => $summary->id,
        ]);

        Log::debug('预警已添加到汇总', [
            'alert_id' => $alert->id,
            'summary_id' => $summary->id,
        ]);
    }

    /**
     * 完成汇总并准备发送通知
     *
     * @param string $collectionBatchId
     * @param int|null $taskGroupId
     * @param int|null $monitoringTaskId
     * @return void
     */
    public function finalizeSummary(string $collectionBatchId, ?int $taskGroupId = null, ?int $monitoringTaskId = null): void
    {
        Log::info('完成预警汇总并准备发送通知', [
            'collection_batch_id' => $collectionBatchId,
            'task_group_id' => $taskGroupId,
            'monitoring_task_id' => $monitoringTaskId,
        ]);

        // 构建查询条件
        $query = TaskGroupAlertSummary::where('collection_batch_id', $collectionBatchId)
            ->where('status', TaskGroupAlertSummary::STATUS_COLLECTING);

        if ($taskGroupId) {
            $query->where('task_group_id', $taskGroupId);
        }

        if ($monitoringTaskId) {
            $query->where('monitoring_task_id', $monitoringTaskId);
        }

        $summaries = $query->get();

        foreach ($summaries as $summary) {
            $this->finalizeSingleSummary($summary);
        }
    }

    /**
     * 完成单个汇总
     *
     * @param TaskGroupAlertSummary $summary
     * @return void
     */
    private function finalizeSingleSummary(TaskGroupAlertSummary $summary): void
    {
        Log::info('完成单个预警汇总', [
            'summary_id' => $summary->id,
            'alert_count' => $summary->alert_count,
        ]);

        // 如果没有预警，直接标记为已发送
        if (!$summary->hasAlerts()) {
            Log::debug('汇总没有预警，跳过通知发送', [
                'summary_id' => $summary->id,
            ]);
            $summary->markSent();
            return;
        }

        // 标记为准备发送
        $summary->markReady();

        // 分发通知发送任务
        $this->dispatchNotification($summary);
    }

    /**
     * 分发通知发送任务
     *
     * @param TaskGroupAlertSummary $summary
     * @return void
     */
    private function dispatchNotification(TaskGroupAlertSummary $summary): void
    {
        try {
            SendTaskGroupAlertNotification::dispatch($summary->id)
                ->delay(now()->addSeconds(1)); // 延迟1秒执行

            Log::info('任务分组预警通知任务已分发到队列', [
                'summary_id' => $summary->id,
                'queue_connection' => config('queue.default'),
            ]);
        } catch (\Exception $e) {
            Log::error('分发任务分组预警通知任务失败', [
                'summary_id' => $summary->id,
                'error' => $e->getMessage(),
            ]);
            
            $summary->markFailed();
        }
    }

    /**
     * 生成采集批次ID
     *
     * @param int $monitoringTaskId
     * @return string
     */
    public function generateCollectionBatchId(int $monitoringTaskId): string
    {
        return "task_{$monitoringTaskId}_" . now()->format('YmdHis') . '_' . uniqid();
    }

    /**
     * 检查监控任务是否完成采集
     *
     * @param int $monitoringTaskId
     * @param string $collectionBatchId
     * @return bool
     */
    public function isMonitoringTaskCollectionComplete(int $monitoringTaskId, string $collectionBatchId): bool
    {
        Log::info('检查监控任务是否完成采集', [
            'monitoring_task_id' => $monitoringTaskId,
            'collection_batch_id' => $collectionBatchId,
        ]);

        $monitoringTask = MonitoringTask::find($monitoringTaskId);
        if (!$monitoringTask) {
            Log::warning('监控任务不存在', ['monitoring_task_id' => $monitoringTaskId]);
            return false;
        }

        // 获取任务的目标商品列表
        $targetProducts = $monitoringTask->target_products ?? [];
        if (empty($targetProducts)) {
            Log::info('监控任务没有目标商品，认为已完成', ['monitoring_task_id' => $monitoringTaskId]);
            return true;
        }

        // 提取商品ID列表
        $productIds = [];
        foreach ($targetProducts as $product) {
            if (is_array($product) && isset($product['product_id'])) {
                $productIds[] = $product['product_id'];
            } elseif (is_string($product) || is_numeric($product)) {
                $productIds[] = $product;
            }
        }

        if (empty($productIds)) {
            Log::info('没有有效的商品ID，认为已完成', ['monitoring_task_id' => $monitoringTaskId]);
            return true;
        }

        // 检查是否所有目标商品都已完成采集
        // 这里我们检查在当前采集批次中是否所有商品都有对应的ProductData记录
        $collectedProductCount = ProductData::where('monitoring_task_id', $monitoringTaskId)
            ->whereIn('item_id', $productIds)
            ->where('last_collected_at', '>=', now()->subHours(1)) // 最近1小时内采集的数据
            ->count();

        $isComplete = $collectedProductCount >= count($productIds);

        Log::info('监控任务采集完成检查结果', [
            'monitoring_task_id' => $monitoringTaskId,
            'collection_batch_id' => $collectionBatchId,
            'target_product_count' => count($productIds),
            'collected_product_count' => $collectedProductCount,
            'is_complete' => $isComplete,
        ]);

        return $isComplete;
    }

    /**
     * 清理过期的汇总记录
     *
     * @param int $daysOld
     * @return int
     */
    public function cleanupOldSummaries(int $daysOld = 30): int
    {
        $cutoffDate = now()->subDays($daysOld);
        
        $deletedCount = TaskGroupAlertSummary::where('created_at', '<', $cutoffDate)
            ->where('status', TaskGroupAlertSummary::STATUS_SENT)
            ->delete();

        Log::info('清理了过期的预警汇总记录', [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate->toDateTimeString(),
        ]);

        return $deletedCount;
    }

    /**
     * 获取汇总统计信息
     *
     * @param int|null $taskGroupId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return array
     */
    public function getSummaryStatistics(?int $taskGroupId = null, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        $query = TaskGroupAlertSummary::query();

        if ($taskGroupId) {
            $query->where('task_group_id', $taskGroupId);
        }

        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo . ' 23:59:59');
        }

        $totalSummaries = $query->count();
        $sentSummaries = (clone $query)->where('status', TaskGroupAlertSummary::STATUS_SENT)->count();
        $totalAlerts = (clone $query)->sum('alert_count');
        $totalProducts = (clone $query)->sum('product_count');

        return [
            'total_summaries' => $totalSummaries,
            'sent_summaries' => $sentSummaries,
            'total_alerts' => $totalAlerts,
            'total_products' => $totalProducts,
            'success_rate' => $totalSummaries > 0 ? round(($sentSummaries / $totalSummaries) * 100, 2) : 0,
        ];
    }
}
